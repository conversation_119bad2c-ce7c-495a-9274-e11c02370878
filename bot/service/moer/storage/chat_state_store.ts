import { ChatState } from './chat_state'
import { ChatDB } from '../database/chat'
import { MoerNode } from '../components/flow/nodes/type'
import logger from '../../../model/logger/logger'
import { LRUCache } from 'lru-cache'

export enum ConversationState { // 会话状态，用于替换 Flag, 假设同一时间同一客户只能有一个状态，并发下会进入到同一节点
  QueryAskBeforeClass = 'QueryAskBeforeClass',
  Start = 'Start', // 开始
  SendPhoneQuery = 'SendPhoneQuery',
  SendEnergyTest = 'SendEnergyTest',
  CompleteEnergyTest = 'CompleteEnergyTest', // 完成能量测试
  SendPreCourseCompletionGift = 'SendPreCourseCompletionGift', // 发送 完课礼
  End = 'Ended', // 结束
}

export interface IChattingState {
  [key: string]: boolean | undefined  // 注意这里是为了 prisma 类型检查，不要删除

  is_add_big_plan_tasks?: boolean // 是否添加大 Planner 每日定时任务

  is_bounding_phone_number?: boolean // 正在绑定手机号
  is_send_energy_test? : boolean

  send_pre_course_message?: boolean // 发送小讲堂
  is_delayed_send_energy_test?: boolean // 是否延迟发送能量测试

  is_add_tasks?: boolean // 是否已经添加过任务
  is_friend_accepted?: boolean // 是否处理过添加好友

  handled_failed_payment?: boolean // 处理过支付失败

  is_complete_post_sale?: boolean // 是否完成售后

  is_send_pre_course_completion_gift?: boolean // 发送过小讲堂完课礼
  has_send_class1_reward?: boolean
  has_send_class2_reward?: boolean
  has_send_class3_reward?: boolean

  is_send_user_query1?: boolean
  is_complete_user_query?: boolean // 是否完成挖需
  is_complete_user_query_before_class?: boolean // 是否完成挖需2， 课前挖需
  is_complete_energy_test?: boolean // 是否完成能量测试

  is_complete_pre_course?: boolean // 是否完成小讲堂
  is_complete_day1_course?: boolean
  is_complete_day1_course_recording?: boolean
  is_complete_day2_course?: boolean
  is_complete_day2_course_recording?: boolean
  is_complete_day3_course?: boolean
  is_complete_day3_course_recording?: boolean
  is_complete_day4_course?: boolean
  is_complete_day4_course_recording?: boolean

  is_attend_pre_course_one_second?:boolean
  is_attend_day1_course_one_second?:boolean
  is_attend_day1_course_recording_one_second?:boolean
  is_attend_day2_course_one_second?:boolean
  is_attend_day2_course_recording_one_second?:boolean
  is_attend_day3_course_one_second?:boolean
  is_attend_day3_course_recording_one_second?:boolean
  is_attend_day4_course_one_second?:boolean
  is_attend_day4_course_recording_one_second?:boolean

  is_complete_day1_homework?: boolean
  is_complete_day2_homework?: boolean

  is_complete_payment?: boolean // 是否完成支付

  is_in_day1_class_live_stream?: boolean // 是否在第一天直播课程中
  is_in_day2_class_live_stream?: boolean // 是否在第二天直播课程中
  is_in_day3_class_live_stream?: boolean // 是否在第三天直播课程中
  is_in_day4_class_live_stream?: boolean // 是否在第四天直播课程中

  is_complete_energy_test_analyze?: boolean // 是否完成能量测评解读
  is_complete_wealth_orchard_analyze?: boolean // 是否完成财富果园解读
  in_class_wealth_orchard_template_send?: boolean // 是否在课堂中发送财富果园模板


  is_complete_day1_homework_feedback?: boolean // 是否完成第一天作业打卡解读
  is_complete_day2_homework_feedback?: boolean // 是否完成第二天作业打卡解读
  is_complete_day3_homework_feedback?: boolean // 是否完成第三天作业打卡解读
  is_complete_day4_homework_feedback?: boolean // 是否完成第四天作业打卡解读

  is_send_installment_video?: boolean // 是否发送分期付款视频
}

export interface IUserSlot { // 客户信息槽位参考
  [key: string]: any  // 注意这里是为了 prisma 类型检查，不要删除

  meditation_goal?: string
  live_class_confirmation?: string
  energy_test_score?: number
  phoneNumber?: string
  course_no?: number
  source?: string
  day1_homework?: string
  day2_homework?: string

  meditation_experience?: string[]
  goals_and_needs?: string[]
  pain_points?: string[]
  purchaseHesitation?: string[]

  is_willing_to_purchase?: boolean
  last_enter_live_time_day1?: number
  last_enter_live_time_day2?: number
  last_enter_live_time_day3?: number
  last_enter_live_time_day4?: number

  // 新增字段：客户基本信息
  avatar_analysis?: string  // 头像分析结果
  gender?: string          // 性别：'男'、'女'、'未知'
  wx_nickname?: string     // 微信昵称
}

export interface ICustomSlot {
  topic: string
  subTopic: string
  content: string
  frequency: number
}

export interface IChatState {
  slotAskedCount: Record<string, number> // 记录每个槽位被问的次数
  nodeInvokeCount: Record<string, number> // 记录每个节点被调用的次数
  progress: ConversationState // 当前进行到哪个阶段的提示
  state: IChattingState // 会话状态，放一些 Flag 已拉群，已邀请入群等

  nextStage: string // 下个节点
  userSlots: IUserSlot // 客户具体信息槽位
  danmuUserSlots?: Record<string, contentWithFrequency>
  moreUserSlots?: Record<string, contentWithFrequency>
  customerPortrait?: Record<string, any>
  salesNote?: string // 销售小记
}

export type contentWithFrequency = {
  content: string
  frequency: number
}

export class ChatStatStoreManager {
  public static stateInitialized = new LRUCache({
    max: 300,                 // 最大存储 300 个数据
  })

  /**
   * 初始化状态，只要数据库中有记录，就会初始化。只会调用数据库初始化一次。
   * @param chat_id
   * @param force
   */
  public static async initState(chat_id: string, force = false) {
    if (force || (!this.stateInitialized.get(chat_id) && await ChatDB.getById(chat_id))) { // 从数据库中读取配置，进行覆盖
      const chat = await ChatDB.getById(chat_id)
      if (!chat)
        return

      ChatStateStore.set(chat_id, chat.chat_state)
      ContactNameStore.setContactName(chat_id, chat.contact.wx_name)

      this.stateInitialized.set(chat_id, true)

      console.log(`[ChatStateStore] 初始化状态 ${chat_id}`)
    }
  }

  public static async clearState(chat_id: string) { // 强行将内存中状态重置
    ChatStateStore.clear(chat_id)
    // 防止从数据库读取状态
    this.stateInitialized.set(chat_id, true)
  }
}

export class WealthOrchardStore {
  private static userMessages = new ChatState<string[]>()  // 对客户的消息进行暂存

  public static addUserMessage(chat_id: string, message: string) {
    const messages = this.userMessages.get(chat_id) || []
    messages.push(message)
    this.userMessages.set(chat_id, messages)
  }

  public static getUserMessages(chat_id: string) {
    return this.userMessages.get(chat_id) || []
  }

  public static clearUserMessages(chat_id: string) {
    this.userMessages.set(chat_id, [])
  }
}

export class ContactNameStore {
  private static userNames = new ChatState<string>()  // 对客户的消息进行暂存

  public static setContactName(chat_id: string, name: string) {
    this.userNames.set(chat_id, name)
  }

  public static getContactName(chat_id: string) {
    return this.userNames.get(chat_id)
  }
}


/**
 * 这里的 State 是内存中的状态，每轮对话后，刷新到数据库中
 * 初始化时后，可以从数据库中读取
 */
export class ChatStateStore {
  private static state = new ChatState<IChatState>()

  public static hasState(chat_id: string) {
    return this.state.get(chat_id) !== undefined
  }

  public static getFlags(chat_id: string) {
    return this.get(chat_id).state as IChattingState
  }

  public static get(chat_id: string) {
    if (!this.state.has(chat_id)) {
      this.state.set(chat_id, {
        nextStage: MoerNode.FreeTalk,
        nodeInvokeCount: {},
        slotAskedCount: {},
        progress: ConversationState.Start,
        state: {},
        userSlots: {},
        danmuUserSlots: {},
        moreUserSlots: {},
        customerPortrait: {},
      })

      if (!ChatStatStoreManager.stateInitialized.get(chat_id)) {
        console.warn('未从数据库中读取状态，初始化状态', chat_id)
      }
    }

    return this.state.get(chat_id) as IChatState
  }

  public static clear(chat_id: string) {
    this.state.delete(chat_id)
  }

  /**
   * 注意 name 传递的是类名
   * @param chat_id
   * @param name
   */
  public static getNodeCount(chat_id: string, name: string) {
    const state = this.get(chat_id)
    if (!state.nodeInvokeCount[name]) {
      state.nodeInvokeCount[name] = 0
    }
    return state.nodeInvokeCount[name]
  }

  public static increNodeCount(chat_id: string, name: string) {
    const state = this.get(chat_id)
    // 初始化节点计数为0（如果还不存在）
    if (!state.nodeInvokeCount[name]) {
      state.nodeInvokeCount[name] = 0
    }
    // 增加调用次数
    state.nodeInvokeCount[name] += 1
    // 保存更新后的 state
    this.set(chat_id, state)
  }

  public static set(chat_id: string, state: IChatState) {
    this.state.set(chat_id, state)
  }

  public static update (chat_id: string, partialState: Partial<IChatState>): void {
    logger.trace({ chat_id }, '更新状态:', JSON.stringify(partialState))

    const currentState = this.get (chat_id)
    const updatedState = this.deepMerge (currentState, partialState)
    this.set (chat_id, updatedState as IChatState)
  }

  private static deepMerge (target: any, source: any): any {
    const output = { ...target }
    if (Array.isArray(target) && Array.isArray(source)) {
      return source
    } else if (typeof target === 'object' && typeof source === 'object') {
      for (const key in source) {
        if (source [key] instanceof Object && target instanceof Object && key in target) {
          output [key] = this.deepMerge (target [key], source [key])
        } else {
          output [key] = source [key]
        }
      }
    }
    return output
  }

}