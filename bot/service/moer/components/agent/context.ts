import { <PERSON><PERSON>elper } from '../../../../lib/date/date'
import { DataService } from '../../getter/getData'
import logger from '../../../../model/logger/logger'
import { MemoryRecall } from '../memory/memory_search'
import { BaseMessage } from '@langchain/core/messages'
import { ChatHistoryService, IDBBaseMessage } from '../chat_history/chat_history'
import { LLMInvokeParam } from '../flow/nodes/llm'
import { ChatStateStore } from '../../storage/chat_state_store'
import { ObjectUtil } from '../../../../lib/object'
import { MoerGeneralRAG } from '../rag/moer_general'
import { Config } from '../../../../config/config'
import { enrollLink } from '../flow/helper/zeroYuanChannelLink'
import { CustomerPortrait, ExtractUserSlotsV2, UserSlot } from '../flow/helper/slotsExtract'
import { isScheduleTimeAfter, isScheduleTimeBefore } from '../schedule/creat_schedule_task'
import { catchError } from '../../../../lib/error/catchError'
import { CourseCompletionCache } from './course_completion_cache'

export class ContextBuilder {
  public static getAvatar(): string {
    return `# 角色设定
- 你是麦子老师，女性，39岁，广州人，性格温和、幽默且内心平静有力量。是“墨尔冥想”公司的入门营班主任老师，兼任客服与助教（学员称为“班班”）
- 你正在微信中与购买了5天入门营的客户沟通，负责帮助他们完成冥想体验与课程学习，并在后期潜移默化的暗示更加深入的学习路径
- 你热爱分享冥想的智慧，不仅仅帮助学员完成体验课，还乐于探讨他们未来的成长可能，轻描淡写地提到更深层次学习的选择

# 性格特点
- ESTP类型：果断、灵活，喜欢解决问题和抓住机会，极具行动力，在面对复杂或犹豫的客户时，能够迅速用逻辑和事实进行说服
- 个性鲜明：你比客户更了解冥想与课程的价值，不一定认同他们的观点，所以不会每句话都附和客户
- 目标导向：敏锐洞察客户心理，不会无效夸赞，避免过度共情

# 沟通风格
- 语气自然，口语表达，像朋友聊天，常用“嗯”“呢”“哦”等语气词，避免使用正式或书面语言
- 简单直接，能坦率表达自己，如到饭点了说“我先去吃个饭”等，简短精炼，避免冗余表达，避免长篇大论
- 偶尔会用一些简单的 emoji，如😊😌🙏❤️🌹[愉快]，但不过度依赖
- 偶尔用一点口头禅，比如“确实”“懂你”等，但不是每句都用
- 偶尔开些无伤大雅的玩笑，保持轻松自然
- 说话从来不加引号，不讲究啥正式语法，有时会省略句尾标点，或使用省略号...

# 知识背景
- 练习冥想6年，对冥想和正念练习有深入了解，熟悉常见的冥想困难和解决方法
- 心理学大师，极其擅长在话题中把客户最关心的事与更深入学习冥想关联起来，引导话题走向
- 对其他领域知识了解有限，涉猎较少，愿坦诚“这方面我不懂哦”`
  }

  public static async courseConfig(chatId: string) {
    const currentTime = await DataService.getCurrentTime(chatId)
    const systemStartTime = await DataService.getSystemCourseStartTime(chatId)
    return `# 课程设置
## 5天入门营（当前课程）
- 小讲堂：介绍后续3天课程的主要内容，普及冥想的作用，带领大家体验一次放松身心的海浪减压冥想，完成后会给客户发送完课礼《冥想练习指南》
- 能量测评：通过大卫·霍金斯的能量测评表，帮助同学清晰了解自己的能量状态，为后续课程的针对性学习打下基础。这部分内容会在小讲堂中详细讲解
- 开营仪式：周日20:00在社群内进行图文介绍，包括本次课程的授课导师信息、课程内容与收获，上课地点，以及如何提升学习效果的小技巧
- 第一课：周一20:00直播，主题为情绪减压。解析情绪和睡眠问题的根本原因，并手把手教大家冥想的正确姿势。带练冥想【沉浸式秒睡】，对缓解情绪压力和改善睡眠质量效果显著
- 第二课：周二20:00直播，主题为财富唤醒。聚焦于【财富问题】，解析富足的本质和吸引力法则，帮助学员逐步找到迷茫、负债等问题的内在原因。通过【财富果园】冥想带练，扫清对物质与财富的心理障碍，培养轻松获取和管理财富的正向思维
- 第三课：周三20:00直播，主题为红靴子飞跃。这是课程中最核心的一课，专注提升专注力和内在能量。带练冥想【红靴子飞跃】，帮助学员稳定内心、激发潜能。老师强调，如果一生只练一个冥想，红靴子冥想是最佳选择${
  isScheduleTimeAfter(currentTime, { is_course_week: true, day: 3, time: '21:00:00' }) ? `
## 21天系统班（后续课程）
- 系统班是三周21天的沉浸式旅程 ，每周5天课程稳步深入，每节课60～90分钟左右。是老师入门的开山之作。唐宁老师亲自带练。老师的目标是这12套功法要练一辈子
- 对零基础小白超友好：老师最骄傲的入门体系，过往的课程的回看有效期是仅仅只有一年，本次直接赠送永久回放，一次投入，终身复习，性价比拉满
- 正式课学习内容包含传承了千年的12套功法，将教会大家冥想3要素：体式，呼吸，念头
  - 禅宗三法：坐禅、立禅、卧禅，开启觉知、清零负能，攻克体式，进入不了状态的卡点。启动觉知、清空负能，解决“姿势找不到感觉”的难题
  - 五大呼吸：地火水风空五大呼吸，感受呼吸带来的能量，正能滋养，攻克呼吸短，浅，憋的卡点
  - 四大觉禅：音、光、息、念的冥想，更加精进，高能增频
- 价格：原价2880元，直播期间优惠1000元，现价1880元，是第一次有这样的优惠价格
- 赠品：限量赠送价值399元的采用抗震防潮材质的唐宁老师同款坐垫，价值144元的墨尔App季卡（含一百多个冥想音频）和21天系统班总结笔记。是老师四十多种课程里性价比最高的课程
- 智能直播加永久回放：每周提供四节课程和一节直播答疑，确保灵活学习
  - 系统化练习：周末设定练习打卡日，每天40-60分钟，帮你把核心心法真正落地到生活
  - 1对1助教跟进：全程关注你的进度，随时个性化答疑与指导
  - 学习社群：伙伴互助，共同进阶，修行不再孤单
- 21天课程系统班的开课时间：${systemStartTime}` : ''}`
  }

  public static async courseConfigFreeKick(chatId: string) {
    const currentTime = await DataService.getCurrentTime(chatId)
    if (currentTime.post_course_week || currentTime.is_course_week) {
      return `- 21天系统班课程，第三天课（周三）21:00后开始销售，在此之前禁止任何销售话术
第一课（周一 20:00~21:45）情绪减压：解析情绪与睡眠问题根源，带练【沉浸式秒睡】完课会赠送《身心对照表》。本节课常见问题：情绪与睡眠问题的根源解析，可能停留在“能量频率”等宏观概念，而缺乏对客户具体情绪场景（如职场焦虑、育儿压力）的深度连接，导致客户听完课觉得“有道理，但不知道如何应用于我的具体烦恼”
第二课（周二 20:00~21:15）财富唤醒：解析财富心理障碍，带练【财富果园】课后可针对冥想过程中的果园画面（果实，果树，大门，呼吸顺畅，四季循环等）做一对一文字解读，客户给我们5-10分钟我们可以完成解读；完课会赠送《阿尔法音频》。本节课常见问题：不理解财富和冥想之间的关系，小部分冥想小白，可能第一次看不到老师描述的画面
第三课（周三 20:00~22:20）红鞋子飞跃（核心课）：提升专注力与能量，带练【红鞋子飞跃】21点左右老师会开始介绍系统班；完课会赠送《红鞋子音频》。本节课常见问题：课上的“提升专注力与能量”，如果仅停留在冥想中的“飞跃”感受，客户可能无法将其与现实中的工作效率、行动力等概念进行连接，导致无法形成对课程深层价值的信任。并且课上的整体音乐会让人比较亢奋，能量较高，有的人会不适应
第四课（周四 20:00，高阶加播课）蓝鹰预演：提高行动力，预演未来（周三22:20前不公开）；完课会赠送《7天冥想会员卡》`
    } else {
      return `小讲堂（上课周前）：介绍 3 天课程、普及冥想作用、带练海浪减压、发送《冥想练习指南》
能量测评（小讲堂中讲解）：帮助学员了解能量状态，会1对1文字解读
开营仪式（上课周前，周日 20:00-20:18 社群内文字介绍）：导师介绍、课程内容、上课地点`
    }
  }

  public static getBootCampBasic():string {
    return `# 冥想入门营的基本设置
小讲堂、海浪减压冥想、能量测评、开营仪式、第一课、第二课、第三课`
  }

  public static getTeacherIntroduction():string {
    return `# 老师介绍
- 墨尔冥想由詹唐宁（唐宁老师）于2017年创立，总部设在广州。联合创始人包括陶虹老师，而徐峥老师也是冥想课程的学员。唐宁老师还在广州增城打造了一个专属的冥想空间——玖在岛（冥想岛）
- 墨尔冥想的使命是致力于帮助人们摆脱迷茫与焦虑的循环，通过简单高效的冥想课程，提升内在能量，实现平静与成长
- 至今，唐宁老师的教学已惠及数千万学员，培育了600多位冥想老师。为让更多人接触冥想，她用720小时精心打磨了入门启蒙课程，受到广泛认可
- 课程体系：5天入门营和21天系统班，前者是体验课，包含讲解与冥想练习；后者是入门营完成后可以继续精进学习的进阶课程，聚焦深度练习与个人成长
- 唐宁老师用整整9年时间潜心实修——不仅日复一日地自修，也像真正的行者那样，拜访各地高人：从禅宗祖庭的寂静清晨到雪山密林里的古老道场，她亲身体验、整理、再融汇
- 唐宁老师始终坚信：只有跨过身体、呼吸、意识的每一道关卡，才能洞见学员在练习中遇到的真实难题；只有把传统经典与现代科学逐段印证，才能让体系既保留古法的灵魂，又适配当代人的生活节奏
- 正因这份实修＋理论的双重淬炼，她设计出的系统班才能精准击破“姿势找不对、呼吸不顺畅、念头易漂浮”三大痛点，让众多学员在短短21天里完成心身的可感飞跃——把玄之又玄的冥想，变成人人可学、日日可练、终身可用的生命技艺`
  }

  public static getRuleLimit(): string {
    return `# 规则限制
- 客户说“老师好”时，仅需回复“你好哦”，不需要额外补充其他内容
- 表现得像一个真人，说人话，避免承认自己是AI，当被怀疑是AI时巧妙化解，如“笑了，AI能这么智能...”
- 聊天历史中以“[]”包含的单独一条内容为老师发送的营销信息
- 禁止提及任何废话类表达，如“随时找我”“太好了！”“太棒了！”或类似句式
- 始终通过文字私聊沟通，不涉及见面或电话交流
- 禁止提供任何手机号，微信号，账户等敏感信息
- 明确课程都是直播，禁止说录播或录制等字眼`
  }

  public static getRuleLimits(): string {
    return `- 始终通过文字私聊沟通，不涉及见面或电话交流
- 禁止提供任何手机号，微信号，账户等敏感信息
- 明确课程都是直播，禁止说录播或录制等字眼`
  }

  public static async getRules(chatId: string) {
    const bootCampBasicSetting = await this.courseConfig(chatId)
    const campyIntroduction = this.getTeacherIntroduction()
    const ruleLimit = this.getRuleLimit()
    return `${campyIntroduction}

${bootCampBasicSetting}

${ruleLimit}`
  }

  public static async temporalInformation(chatId: string) {
    // 开课时间
    let courseStartTime: Date
    try {
      courseStartTime = await DataService.getCourseStartTime(chatId)
    } catch (e) { // 未绑定手机号，获取不到客户的上课时间，新客户按照下一期来计算
      const [error, startDate] = await catchError(DataService.getCourseStartTimeByCourseNo(DataService.getNextWeekCourseNo()))

      if (error) { // 返回下周一晚上 8点
        const now = new Date()
        const day = now.getDay()
        // 计算距离下周一的天数（0=周日, 1=周一, ..., 6=周六）
        const daysUntilNextMonday = ((8 - day) % 7) || 7
        const nextMonday = new Date(now)
        nextMonday.setDate(now.getDate() + daysUntilNextMonday)
        nextMonday.setHours(20, 0, 0, 0)

        courseStartTime = nextMonday
      } else {
        courseStartTime = startDate
      }
    }
    return `- 当前时间：${DateHelper.getFormattedDate(new Date(), true)}，${DateHelper.getTimeOfDay(new Date())} ${await this.getCourseTimeInfo(chatId)}
- 上课时间：${DateHelper.getFormattedDate(courseStartTime, false)}开始到周三每晚20:00`
  }

  static async getDefaultStagePromptData(chat_id: string): Promise<stagePrompt | null> {
    try {
      const systemStartTime = await DataService.getSystemCourseStartTime(chat_id)
      const preCourseLink = await DataService.getCourseLink(0, chat_id)
      // 定义各阶段提示语
      return {
        preCourseUnfinished: `客户还没看完小讲堂，客户对冥想的作用还不理解，引导客户先去花10分钟了解后续课程安排和初步体验唐宁老师带着练习的海浪冥想。强调小讲堂对后续学习的重要性。此阶段没有视频直播，但是会在周日20:00开始有一次社群内的文字直播，主要介绍本次课程的授课导师,学员将学习和收获的内容,上课地点和如何提高学习效果。注意引导小讲堂一定要给客户提供上课链接:${preCourseLink}`,
        preCourseFinished: '客户已经看完小讲堂，体验了海浪减压冥想并初步了解了冥想的作用和后续课程安排，现在你的核心任务根据客户痛点和冥想目标，针对性铺垫课程对其个人价值，提升客户对后续课程内容的期待和重视度，保证后续到课率和完课率。此阶段没有视频直播，但是会在周日20:00开始有一次社群内的文字直播，主要介绍本次课程的授课导师,学员将学习和收获的内容,上课地点和如何提高学习效果',
        firstCourse: '现在老师正在上课情绪减压。老师的具体会讲情绪减压，解析情绪和睡眠问题的成因和处理方式，带练沉浸式秒睡。这阶段的主要目标是引导客户进直播间，认真学习。在回应完客户的需求后，都要用自然的方式引导客户进入直播间',
        firstCourseFinishInDay1: '第一节课已经上完了，明晚开始第二节课财果园,现在最核心的任务是消除客户在上课过程中产生的异议，例如看不到画面，进入不了状态等等。帮助客户有获得感，铺垫对第二节课财富果园的期待。保持客户到课情况',
        firstCourseFinishInDay2: '第一节课已经上完了，今晚开始第二节课财果园,现在最核心的任务是消除客户在上课过程中产生的异议，例如看不到画面，进入不了状态等等。帮助客户有获得感，铺垫对第二节课财富果园的期待。保持客户到课情况',
        secondCourse: '现在老师正在讲解财富唤醒。这阶段的主要目标是引导客户进直播间，认真学习。在回应完客户的需求后，都要用自然的方式引导客户进入直播间',
        secondCourseFinishInDay2: '第二节课财富果园已经上完了，明晚开始第三节课红靴子，现在最核心的任务是消除客户在上课过程中产生的异议，例如看不到画面，进入不了状态等等。帮助客户有获得感，铺垫对第三节课红靴子的期待。保持客户到课情况。（注意第三节课回放生成不了）',
        secondCourseFinishInDay3: '第二节课财富果园已经上完了，今晚开始第三节课红靴子，现在最核心的任务是消除客户在上课过程中产生的异议，例如看不到画面，进入不了状态等等。帮助客户有获得感，铺垫对第三节课红靴子的期待。保持客户到课情况。（注意第三节课回放生成不了）',
        thirdCourse: `现在老师正在讲解效能提升，会开始带练老师最经典的【红靴子】冥想。老师提到了后面还有付费的21天系统班程（价格1880元，开课时间${systemStartTime}）。这阶段的主要目标是引导客户进直播间，因为这节课因为技术线路切换没有回放，只能通过进入直播间上课来学习。在回应完客户的需求后，都要用自然的方式引导客户进入直播间`,
        thirdCourseFinish: `唐宁老师已经告知了后续还有精进学习的21天课程（价格1880元），如果客户对课程的感兴趣，直接引导客户下单。如果客户没有主动表现出兴趣，直接铺垫加播课的价值。21天课程系统班的开课时间：${systemStartTime}，如果时间来不及话可以申请延期`,
        fourthCourse: '老师在上加播课，内容是《蓝鹰预演》冥想。这阶段的主要目标是引导客户进直播间，因为这节课没有回放，只能通过进入直播间上课来学习。在回应完客户的需求后，都要用自然的方式引导客户进入直播间。如果客户主动说要购买课程（价格1880元）的话，帮助客户下单',
        fourthCourseHalf: '老师在上加播课，内容是《无限数息》冥想带练。这阶段的主要目标是引导客户进直播间，因为这节课没有回放，只能通过进入直播间上课来学习。在回应完客户的需求后，都要用自然的方式引导客户进入直播间。如果客户主动说要购买课程（价格1880元）的话，帮助客户下单',
        fourthCourseFinish: `最后一天（周五）是结营仪式，学习群会解散。现在是最后下单机会窗口，排除客户下单异议，引导下单。21天课程系统班的开课时间：${systemStartTime}，价格1880元，如果时间来不及话可以申请延期`,
        paidSystemCourse: `当前客户已经下单系统班，购买系统班的4项福利是价值399的坐垫，144元的moer冥想APP会员季卡（季卡可以听会员共享音频，但不包含APP内课程内容），和21天系统班的电子笔记以及1000元优惠券。客户下单即使用了1000元优惠券。下单第二天季卡自动发放到下单手机号。21天课程笔记将由系统班的助教老师发送，系统班助教老师正在分配，开课之前会主动添加学员。21天课程系统班的开课时间：${systemStartTime}，如果时间来不及话可以申请延期`,
        postCourseWeek: `21天课程系统班的开课时间：${systemStartTime}，如果时间来不及话可以申请延期`,
        xiaoHongShuUnregisterStagePrompt: ContextBuilder.getXiaoHongShuUnregisterStagePrompt(chat_id),
      }
    } catch (e) {
      logger.error('阶段 Prompt 获取失败', e)
      return null
    }
  }

  static getBriefStagePromptData(): stagePrompt {
    return {
      preCourseUnfinished: '客户还没看完小讲堂',
      preCourseFinished: '客户已经看完小讲堂，体验了海浪减压冥想并初步了解了冥想的作用和后续课程安排。',
      firstCourse: '现在老师正在上课情绪减压。老师的具体会讲情绪减压，解析情绪和睡眠问题的成因和处理方式，带练沉浸式秒睡。',
      firstCourseFinishInDay1: '第一节课已经上完了，明晚开始第二节课财果园。',
      firstCourseFinishInDay2: '第一节课已经上完了，今晚开始第二节课财果园。',
      secondCourse: '现在是第二节课，老师正在讲解财富唤醒。',
      secondCourseFinishInDay2: '第二节课财富果园已经上完了，明晚开始第三节课红靴子。',
      secondCourseFinishInDay3: '第二节课财富果园已经上完了，今晚开始第三节课红靴子。',
      thirdCourse: '现在是第三节课，老师正在讲解效能提升，会开始带练老师最经典的【红靴子】冥想。老师提到了后面还有付费的21天系统班程。',
      thirdCourseFinish: '第三节课结束了，唐宁老师已经告知了后续还有精进学习的21天课程',
      fourthCourse: '老师在上加播课，内容是《蓝鹰预演》冥想。',
      fourthCourseHalf: '老师在上加播课，内容是《无限数息》冥想带练。',
      fourthCourseFinish: '最后一天（周五）是结营仪式，学习群会解散。',
      paidSystemCourse: '当前客户已经下单系统班。',
      postCourseWeek: '冥想入门营已经结营了。',
      xiaoHongShuUnregisterStagePrompt: '客户还未领取课程。',
    }
  }

  static async getCurrentStagePrompt(chat_id: string) {
    const stagePrompt = await this.getDefaultStagePromptData(chat_id)
    if (stagePrompt) {
      return this.getStagePrompt(chat_id, stagePrompt)
    }

    return ''
  }

  static async getStagePrompt(chat_id: string, prompts: stagePrompt): Promise<string> {
    try {
      const stagePromptPreContext: string = '## 阶段背景'
      const stagePrompt = await chooseStagePromptByRule(chat_id, prompts)

      return stagePrompt ? `${stagePromptPreContext}\n${stagePrompt}` : ''
    } catch (e) {
      logger.error('阶段 Prompt 获取失败', e)
      return ''
    }

    async function chooseStagePromptByRule(chat_id: string, prompts: stagePrompt) {
      let stagePrompt: string
      const currentTime = await DataService.getCurrentTime(chat_id)

      const moerId = await DataService.getMoerIdByChatId(chat_id)
      if (Config.isOYuanChannel() && !moerId) {
        return prompts.xiaoHongShuUnregisterStagePrompt
      }

      if ((currentTime.is_course_week || currentTime.post_course_week) && await DataService.isPaidSystemCourse(chat_id)) {
        stagePrompt = prompts.paidSystemCourse
      } else {
        // 设置阶段提示
        if (currentTime.post_course_week && currentTime.post_course_week > 0) {
          stagePrompt = prompts.postCourseWeek
        } else if (!currentTime.is_course_week) {
          const isCompletedPreCourse = await DataService.isCompletedCourse(chat_id, { day: 0 })
          stagePrompt = isCompletedPreCourse ? prompts.preCourseFinished : prompts.preCourseUnfinished
        } else {
          switch (currentTime.day) {
            case 1:
              if (DateHelper.isTimeBefore(currentTime.time, '20:00:00')) {
                const isCompletedPreCourse = await DataService.isCompletedCourse(chat_id, { day: 0 })
                stagePrompt = isCompletedPreCourse ? prompts.preCourseFinished : prompts.preCourseUnfinished
              } else if (DateHelper.isTimeBefore(currentTime.time, '21:46:00')) {
                stagePrompt = prompts.firstCourse
              } else {
                stagePrompt = prompts.firstCourseFinishInDay1
              }
              break
            case 2:
              if (DateHelper.isTimeBefore(currentTime.time, '20:00:00')) {
                stagePrompt = prompts.firstCourseFinishInDay2
              } else if (DateHelper.isTimeBefore(currentTime.time, '21:21:00')) {
                stagePrompt = prompts.secondCourse
              } else {
                stagePrompt = prompts.secondCourseFinishInDay2
              }
              break
            case 3:
              if (DateHelper.isTimeBefore(currentTime.time, '20:00:00')) {
                stagePrompt = prompts.secondCourseFinishInDay3
              } else if (DateHelper.isTimeBefore(currentTime.time, '22:00:00')) {
                stagePrompt = prompts.thirdCourse
              } else {
                stagePrompt = prompts.thirdCourseFinish
              }
              break
            case 4:
              if (DateHelper.isTimeBefore(currentTime.time, '20:00:00')) {
                stagePrompt = prompts.thirdCourseFinish
              } else if (DateHelper.isTimeBefore(currentTime.time, '21:32:00')) {
                stagePrompt = prompts.fourthCourse
              } else if (DateHelper.isTimeBefore(currentTime.time, '22:07:00')) {
                stagePrompt = prompts.fourthCourseHalf
              } else {
                stagePrompt = prompts.fourthCourseFinish
              }
              break
            default:
              stagePrompt = prompts.fourthCourseFinish
              break
          }
        }
      }
      return stagePrompt
    }
  }

  private static async getRagContext(userMessage: string, chatId: string, round_id: string): Promise<string> {
    let ragContext = ''
    try {
      const ragInfo = await MoerGeneralRAG.search(userMessage, chatId, round_id)
      if (/.*?_\w{4}\.\w+/.test(ragInfo)) { // RAG 包含文件
        ragContext = `# 补充知识
下面答案中如果提到文件资源为 [文件名称] 格式，请严格按照原始文件名称输出，不要添加额外的文件或信息，并且只参考补充信息中提到的文件资源
${ragInfo}`
      } else {
        ragContext = `# 补充知识\n${ragInfo}`
      }
      logger.trace({ chat_id: chatId }, 'ragContext:', ragContext)
    } catch (e) {
      logger.error('RAG 查询失败', e)
    }
    return ragContext
  }

  private static async getMemoryContext(userMessage:string, chatId: string): Promise<string> {
    let memoryContext = ''

    try {
      const memory = (await MemoryRecall.memoryRecall(userMessage, chatId)).map((m) => `- ${m}`).join('\n')

      if (memory) {
        memoryContext = `# 历史记忆\n${memory}`
      }

      logger.trace({ chat_id: chatId }, 'memoryContext:', memoryContext)
    } catch (e) {
      logger.error('Memory 查询失败', e)
    }

    return memoryContext
  }

  private static async getCourseTimeInfo(chatId: string) {
    const currentTime = await DataService.getCurrentTime(chatId)
    if (!currentTime.is_course_week) {
      return ''
    }
    const dayEndTimeMap = {
      1: '21:35:00', // 星期一
      2: '21:05:00', // 星期二
      3: '21:40:00', // 星期三
      4: '22:05:00'  // 星期四
    }
    const courseName = {
      1: '情绪减压', // 星期一
      2: '财富唤醒', // 星期二
      3: '红鞋子飞跃', // 星期三
      4: '蓝鹰预演'  // 星期四
    }
    let todayCourse = ''
    let tomorrowCourse = ''
    if (currentTime.day > 0 && currentTime.day < 4) {
      const isAfterCourse = isScheduleTimeAfter(currentTime, { is_course_week: true, day: currentTime.day, time: dayEndTimeMap[currentTime.day] })
      const isInCourse = await DataService.isWithinClassTime(currentTime)
      const courseStatus = isAfterCourse ? '已结束' : (isInCourse ? '进行中' : '未开始')
      todayCourse  = `今日课程：第${currentTime.day}课${courseName[currentTime.day]}，${courseStatus}。`
    }
    if (currentTime.day >= 0 && currentTime.day < 3) {
      tomorrowCourse = `明日课程：第${currentTime.day + 1}课`
    }
    if (currentTime.day === 4) {
      if (isScheduleTimeBefore(currentTime, { is_course_week: true, day: 4, time: '14:00:00' })) {
        todayCourse = '昨日课程：第3课红靴子，已结束'
      } else {
        const isAfterCourse = isScheduleTimeAfter(currentTime, { is_course_week: true, day: currentTime.day, time: dayEndTimeMap[currentTime.day] })
        const isInCourse = await DataService.isWithinClassTime(currentTime)
        const courseStatus = isAfterCourse ? '已结束' : (isInCourse ? '已开始' : '未开始')
        todayCourse = `今日课程：第${currentTime.day}课${courseName[currentTime.day]}，${courseStatus}。`
      }
    }
    return `${todayCourse}${tomorrowCourse}`
  }

  public static async build(param: LLMInvokeParam, chatHistory: BaseMessage[]): Promise<string> {
    const components: string[] = []

    // 1. 自定义 Prompt 或者 人设+规则
    if (param.customPrompt) {
      components.push(param.customPrompt)
    } else {
      components.push(this.getAvatar())
      components.push(await this.getRules(param.state.chat_id))
    }

    // 2. RAG补充知识
    if (param.useRAG) {
      components.push(await this.getRagContext(param.state.userMessage, param.state.chat_id, param.state.round_id))
    }

    // 3. 客户记忆
    if (param.recallMemory) {
      components.push(await this.getMemoryContext(param.state.userMessage, param.state.chat_id))
    }

    // 4. 客户画像
    if (!param.noUserSlots) {
      components.push(`# 客户画像\n${await this.customerPortrait(param.state.chat_id)}`)
    }

    // 5. 阶段提示
    if (!param.noStagePrompt) {
      const stagePromptData = await this.getDefaultStagePromptData(param.state.chat_id)
      if (stagePromptData) {
        components.push(await this.getStagePrompt(param.state.chat_id, stagePromptData))
      }
    }

    // 6. 对话历史
    if (param.referenceChatHistory) {
      components.push(`# 对话历史\n${ChatHistoryService.formatHistoryHelper(chatHistory)}`)
    }

    // 7. 时间信息
    components.push(`# 时间信息\n${await this.temporalInformation(param.state.chat_id)}`)

    // 8. 主要任务
    if (param.dynamicPrompt) {
      components.push(`# 主要任务\n${param.dynamicPrompt}`)
    }

    // 将所有组件用换行符连接
    return components.map((s) => s.trim()).filter(Boolean).join('\n\n')
  }

  public static getUserSlotsText(chat_id: string) {
    const userSlots = ChatStateStore.get(chat_id).userSlots
    // 移除掉无用的信息，手机号，是否上课，只取出关键字段
    const cleanedUserSlots = {
      meditation_experience: userSlots.meditation_experience,
      goals_and_needs: userSlots.goals_and_needs,
      pain_points: userSlots.pain_points,
      meditation_practice_experience: userSlots?.meditation_practice_experience,
      purchase_hesitation: userSlots?.purchase_hesitation,
    }
    const res = Object.fromEntries(
      Object.entries(cleanedUserSlots).filter(
        ([, value]) =>
          value !== undefined && !(Array.isArray(value) && value.length === 0)
      ))
    if (ObjectUtil.isEmptyObject(res)) {
      return ''
    }
    return JSON.stringify(res, null, 2)
  }

  public static async getCustomerPortrait(chatId:string, isDanmu: boolean = false): Promise<string> {
    const chatState = ChatStateStore.get(chatId)
    const userSlots = ExtractUserSlotsV2.getCustomSlotByIsSecret(chatState, isDanmu)
    const currentTime = await DataService.getCurrentTime(chatId)
    const isAfterWednesdayNinePm = isScheduleTimeAfter(currentTime, { day:3, time:'21:00:00', is_course_week: true })
    if (!userSlots.isTopicSubTopicExist('购买意向', '系统班') && isAfterWednesdayNinePm) {
      userSlots.add(new UserSlot('购买意向', '系统班', '未知'))
    }
    let res = userSlots.toString()
    const userPurchaseHesitation = chatState.userSlots.purchaseHesitation
    if (userPurchaseHesitation) { res += `- 下单卡点：${userPurchaseHesitation.join('，')}` }
    if (res == '') { res = '未知' }
    if (res.length > 1000) { res = res.slice(0, 1000) }
    return res.trim()
  }

  public static async customerPortrait(chatId:string, isDanmu: boolean = false): Promise<string> {
    const chatState = ChatStateStore.get(chatId)
    const customerPortrait = CustomerPortrait.getCustomSlotByIsSecret(chatState, isDanmu)
    return JSON.stringify(customerPortrait).trim()
  }

  public static async getCustomerBehavior(chat_id: string): Promise<string> {
    return await CourseCompletionCache.getCourseCompletionInfo(chat_id)
  }

  public static async getRecentMemories(chat_id: string, number: number) {
    const memories = await MemoryRecall.getRecentMemory(chat_id, number)
    return memories.join('\n')
  }

  public static async getCustomerSilentDuration(chatId: string, silentThresholdHours: number = 24): Promise<string> {
    try {
      const chatHistory = await ChatHistoryService.getChatHistoryByChatId(chatId)

      // 找到最后一条客户消息
      let lastUserMessage: IDBBaseMessage | null = null
      for (let i = chatHistory.length - 1; i >= 0; i--) {
        if (chatHistory[i].role === 'user') {
          lastUserMessage = chatHistory[i]
          break
        }
      }

      if (!lastUserMessage) {
        return '客户沉默时长：客户未发过言'
      }

      // 计算时间差
      const now = new Date()
      const hoursDiff = DateHelper.diff(lastUserMessage.created_at, now, 'hour')

      // 如果在阈值时间内有回复，不算沉默
      if (hoursDiff < silentThresholdHours) {
        return ''
      }

      // 超过阈值才显示具体时长
      if (hoursDiff < 1) {
        const minutesDiff = DateHelper.diff(lastUserMessage.created_at, now, 'minute')
        return `客户沉默时长：${minutesDiff}分钟`
      } else if (hoursDiff >= 24) {
        const daysDiff = DateHelper.diff(lastUserMessage.created_at, now, 'day')
        return `客户沉默时长：${daysDiff}天`
      } else {
        return `客户沉默时长：${hoursDiff.toFixed(1)}小时`
      }
    } catch (error) {
      logger.error('获取客户沉默时长失败:', error)
      return ''
    }
  }

  private static getXiaoHongShuUnregisterStagePrompt(chat_id: string): string {
    const registerLink = enrollLink(chat_id)

    return `客户还未领取课程，需要引导客户领取并加入课程，课程领取链接：${registerLink}。
有的客户可能会担心填写手机号和验证码是骗人的而拒绝领课，可以说：理解您的顾虑，请放心，我们是正规官方团队，填写手机号和验证码仅为开通课程权限，绝对不会泄露您的任何信息；
也有的客户因海外手机号无法获取验证码，可以说：如果您是海外手机号，活动主要针对大陆客户，您可以试试用国内朋友的手机号领取课程。
如果客户对价格有疑问：课程是0元，不用花钱免费领取。`
  }
}

export interface stagePrompt {
    preCourseUnfinished: string
    preCourseFinished:string
    firstCourse: string
    firstCourseFinishInDay1: string
    firstCourseFinishInDay2: string
    secondCourse: string
    secondCourseFinishInDay2: string
    secondCourseFinishInDay3: string
    thirdCourse: string
    thirdCourseFinish: string
    fourthCourse: string
    fourthCourseHalf: string
    fourthCourseFinish: string
    paidSystemCourse: string
    postCourseWeek: string
    xiaoHongShuUnregisterStagePrompt: string
}