import { LLM } from '../../../../../lib/ai/llm/LLM'
import logger from '../../../../../model/logger/logger'

/**
 * 任务调度输入接口
 */
export interface ScheduleTaskInput {
  user_profile: string
  current_time: string // YYYY-MM-DD HH:MM:SS
  tasks_to_schedule: TaskToSchedule[]
  existing_schedule: ExistingTask[]
}

/**
 * 待调度任务
 */
export interface TaskToSchedule {
  task_id: string
  task_description: string
}

/**
 * 已存在的任务
 */
export interface ExistingTask {
  time: string // YYYY-MM-DD HH:MM:SS
  description: string
}

/**
 * 任务角色分配结果
 */
export interface TaskIdentificationResult {
  proactive_tasks: TaskWithReasoning[]
  reactive_materials: TaskWithReasoning[]
}

/**
 * 带推理的任务
 */
export interface TaskWithReasoning {
  reasoning: string
  task_id: string
}

/**
 * 调度计划项
 */
export interface SchedulePlanItem {
  task_id: string
  urgency_level: 'urgent' | 'normal'
  task_type: 'daily_greeting' | 'pre_class_reminder' | 'post_class_follow_up' | 'engagement_prompt' | 'value_delivery'
  scheduled_time: string // YYYY-MM-DD HH:MM:SS 或 "now"
}

/**
 * 任务调度输出接口
 */
export interface ScheduleTaskOutput {
  step1_identification_result: TaskIdentificationResult
  step2_scheduling_plan: SchedulePlanItem[]
}

/**
 * 任务调度器
 */
export class TaskScheduler {
  private llm: LLM

  constructor(chat_id: string, model: string = 'gpt-5', temperature: number = 0) {
    this.llm = new LLM({
      model,
      temperature,
      responseJSON: true,
      promptName: 'TaskScheduler',
      meta: {
        chat_id
      }
    })
  }

  /**
   * 执行任务调度
   * @param input 调度输入
   * @returns 调度结果
   */
  public async scheduleTask(input: ScheduleTaskInput): Promise<SchedulePlanItem[]> {
    try {
      const prompt = this.createPrompt(input)
      const response = await this.llm.predict(prompt)

      // 解析 JSON 响应
      const result = JSON.parse(response) as ScheduleTaskOutput

      return result.step2_scheduling_plan
    } catch (error) {
      logger.error('TaskScheduler error:', error)
      return []
    }
  }

  /**
   * 创建调度 prompt
   * @param input 输入参数
   * @returns prompt 字符串
   */
  private createPrompt(input: ScheduleTaskInput): string {
    return `你的核心职责是分析一个任务列表，并输出一个两步的规划：第一步是识别出适合主动发起的任务并说明理由；第二步是为这些主动任务制定详细的执行时间表。

当前客户正在参与五天入门营，课程的日程安排主要如下：
# 5天入门营（当前课程）
- 课程前一周（预热与破冰)：
  - 小讲堂：客户报名后，会让客户体验一个 10分钟的冥想练习（先导课），带领大家体验一次放松身心的海浪减压冥想，完成后会给客户发送完课礼《冥想练习指南》
  - 能量测评：在听完先导课后，会发放大卫·霍金斯的能量测评表，帮助同学清晰了解自己的能量状态，这部分内容会在先导课中提到过
  - 开营仪式：开课前一周的周日20:00在社群内进行图文介绍，包括本次课程的授课导师信息、课程内容与收获，上课地点，以及如何提升学习效果的小技巧
- 第一课：周一20:00直播，主题为情绪减压。解析情绪和睡眠问题的根本原因，并手把手教大家冥想的正确姿势。带练冥想【沉浸式秒睡】，对缓解情绪压力和改善睡眠质量效果显著
- 第二课：周二20:00直播，主题为财富唤醒。聚焦于【财富问题】，解析富足的本质和吸引力法则，帮助学员逐步找到迷茫、负债等问题的内在原因。通过【财富果园】冥想带练，扫清对物质与财富的心理障碍，培养轻松获取和管理财富的正向思维
- 第三课：周三20:00直播，主题为红靴子飞跃。这是课程中最核心的一课，专注提升专注力和内在能量。带练冥想【红靴子飞跃】，帮助学员稳定内心、激发潜能。老师强调，如果一生只练一个冥想，红靴子冥想是最佳选择

# 1. 输入格式

你将收到一个JSON对象作为输入，包含以下字段：

-   \`user_profile\`: 当前的客户画像以及最近的聊天记录
-   \`current_time\`: 当前的日期和时间 (YYYY-MM-DD HH:MM:SS)。
-   \`tasks_to_schedule\`: 一个需要你分析和安排的原始任务列表。
-   \`existing_schedule\`: 一个未来已安排好的任务列表。

# 2. 核心处理流程

你的输出必须包含两个步骤，严格按照以下逻辑生成：

# 第一步：任务角色分配与理由阐述

这是你的首要任务。遍历 \`tasks_to_schedule\` 列表中的每一个任务，为它分配一个"沟通角色"（\`PROACTIVE_OUTREACH\` 或 \`REACTIVE_MATERIAL\`），并为你的每一次判断提供清晰的理由。

*   **判断为适合主动发起任务的标准：**
    1. 当前有关键信息未被传递完成
    2. 临近销售节点促进成交、或在特定时间点提醒客户

*   **输出要求：** 在此步骤中，你需要生成一个包含 \`proactive_tasks\` 和 \`reactive_materials\` 两个列表的对象。每个对象内，**必须先输出 \`reasoning\` 字段，再输出 \`task_id\` 字段。**

# 第二步：为主动外呼任务排期

现在，只聚焦于你在第一步中识别出的 \`proactive_tasks\` 列表。对这个列表中的每一个任务，制定详细的执行计划。

*   **调度逻辑：**
    1.  **紧急度评估**: 判断任务是否是响应客户刚刚的实时行为。如果是，其 \`scheduled_time\` 标记为 \`"now"\`。注意，当前时间为深夜或客户画像显示其作息规律不适合立即打扰，当前客户忙等，无明显对话意愿，可结合任务队列安排延后发送。
    2.  **常规任务调度**:
        *   **任务类型分析**: 判断任务属于 \`daily_greeting\`, \`pre_class_reminder\`, \`post_class_follow_up\`, \`engagement_prompt\` 或 \`value_delivery\`。
        *   **选择黄金时间段**: 根据任务类型选择最合适的时间段（例如：问候在上午，提醒在课前，跟进在课后次日，比较花费时间的非紧急行为在午休或者下午茶时间等）。
        *   **确定具体时间点**: 在黄金时间段内选择一个具体时间，并检查与 \`existing_schedule\` 的冲突，确保间隔至少30分钟。如果存在冲突，请在黄金时间段内另选一个时间，或者调整到相邻的时间段。

*   **输出要求：** 在此步骤中，你需要生成一个 \`scheduling_plan\` 列表，其中包含所有主动任务的详细执行安排。

# 3. 输出格式

你的最终输出必须是一个JSON对象，包含 \`step1_identification_result\` 和 \`step2_scheduling_plan\` 两个部分，严格遵循以下结构。

{
  "step1_identification_result": {
    "proactive_tasks": [
      {
        "reasoning": "此处为判断该任务为主动任务的理由。",
        "task_id": "..."
      }
    ],
    "reactive_materials": [
      {
        "reasoning": "此处为判断该任务为被动素材的理由。",
        "task_id": "..."
      }
    ]
  },
  "step2_scheduling_plan": [
    {
      "task_id": "...",
      "urgency_level": "urgent" | "normal",
      "task_type": "...",
      "scheduled_time": "YYYY-MM-DD HH:MM:SS" | "now"
    }
  ]
}

# 重要提醒

1. **时间冲突检查**：在安排新任务时，必须确保与 \`existing_schedule\` 中的任务至少间隔30分钟。
2. **紧急度判断**：如果任务描述中包含"刚刚"、"立即"、"马上"等紧急词汇，且是主动任务，应标记为 urgent 或 scheduled_time 为 "now"。
3. **JSON格式**：输出必须是有效的JSON格式，不要包含任何其他文本。

# 输入数据

${JSON.stringify(input, null, 2)}

请严格按照上述JSON格式输出结果。`
  }
}
