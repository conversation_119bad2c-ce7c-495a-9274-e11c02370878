import { getPrompt } from '../../agent/prompt'
import { LLM } from '../../../../../lib/ai/llm/LLM'
import { ContextBuilder } from '../../agent/context'
import { DataService } from '../../../getter/getData'
import { Planner } from '../plan/planner'
import dayjs from 'dayjs'
import { ICreateTask, TaskManager } from '../task/task_manager'
import { TaskStatus } from '@prisma/client'
import { JSONHelper } from '../../../../../lib/json/json'
import logger from '../../../../../model/logger/logger'
import { ITask, PlanOperations, PlanResponse } from '../types'
import { ChatStateStore, ChatStatStoreManager } from '../../../storage/chat_state_store'
import { MemoryStore } from '../../memory/memory_store'
import { FlowTask } from '../../schedule/silent_requestion'
import { FlowTaskType } from '../../schedule/silent_reask_tasks'
import { DateHelper } from '../../../../../lib/date/date'
import { Config } from '../../../../../config/config'
import { UUID } from '../../../../../lib/uuid/uuid'
import { PlanOperationsSimulator } from './test/plan_operations_simulator'
import { CourseCompletionCache } from '../../agent/course_completion_cache'
import { ChatHistoryService } from '../../chat_history/chat_history'


export class BigPlanner {
  public static async addTasks(chat_id: string) {
    // 只能被添加一次
    if (ChatStateStore.getFlags(chat_id).is_add_big_plan_tasks) {
      return
    }

    // 从课程开始到课程结束, 每天 0点触发
    const now = new Date()
    const courseStartTime = await DataService.getCourseStartTime(chat_id)
    const courseEndTime = dayjs(courseStartTime).add(6, 'day').toDate()

    let cursor = dayjs(courseStartTime).startOf('day')

    while (cursor.isBefore(courseEndTime)) {
      const runAt = cursor.hour(6).minute(0).second(0).millisecond(0).toDate()

      // 计算延迟时间（毫秒）
      const delay = DateHelper.diff(now, runAt, 'millisecond')

      if (delay > 0) {
        await FlowTask.schedule(
          FlowTaskType.BigPlan,
          chat_id,
          delay,   // 这里用毫秒延迟
          undefined,
          {
            independent: true,
            chatActivityChecker: async (chat_id: string) => {
              // 检查最近3分钟内是否有非营销的聊天记录
              return await ChatHistoryService.isLastMessageWithDuration(chat_id, 3, 'minute')
            }
          }
        )
      }

      // 下一天
      cursor = cursor.add(1, 'day')
    }

    ChatStateStore.update(chat_id, {
      state: {
        is_add_big_plan_tasks: true
      }
    })
  }


  private static async buildContext(chat_id: string) {
    await ChatStatStoreManager.initState(chat_id)

    // 实时一下补全 memory 和 userSlots
    if (!Config.setting.localTest) {
      await MemoryStore.extractMemoriesAndUserSlots(chat_id, UUID.v4(), true)
    }

    const userSlots = await ContextBuilder.customerPortrait(chat_id)
    const memory = await ContextBuilder.getRecentMemories(chat_id, 5)
    const userBehavior = await CourseCompletionCache.getDetailedUserBehavior(chat_id)
    const silentAnalyze = await ContextBuilder.getCustomerSilentDuration(chat_id, 24)

    const timeInfo = await DataService.getCurrentTime(chat_id)
    let currentTime = await ContextBuilder.temporalInformation(chat_id)

    if (!timeInfo.is_course_week && !timeInfo.post_course_week) {
      currentTime += '\n当前时间是上课周前'
    }

    const stagePromptData = ContextBuilder.getBriefStagePromptData()
    const stagePrompt = await ContextBuilder.getStagePrompt(chat_id, stagePromptData)
    currentTime += `\n${stagePrompt}`

    // 今天 0 点
    const startOfToday = dayjs().startOf('day').toDate()

    // 今天晚上 0 点（也就是明天 0 点）
    const endOfToday = dayjs().endOf('day').toDate()

    const todayTasks = await Planner.filterTasksByDate(chat_id, startOfToday, endOfToday)

    return {
      user_slots: userSlots,
      memory,
      user_behavior: userBehavior,
      silent_analyze: silentAnalyze,
      current_time: currentTime,
      existing_task: todayTasks.map((task, index) => ({ id: `${index + 1}`, ...task }))
    }
  }

  public static async plan(chat_id: string) {
    const planResult = await this.generatePlan(chat_id)
    if (!planResult) {
      return
    }

    const { planResponse, context } =  planResult

    // 获取当前活跃任务和今日任务用于匹配
    const currentTasks = await TaskManager.getScheduledTasks(chat_id)

    const todayTasks = context.existing_task

    const { plans, think } = planResponse

    try {
      await this.executePlanOperations(chat_id, plans, currentTasks, todayTasks, think)

      // 打印操作前后结果
      const res = PlanOperationsSimulator.simulateOperations(context.existing_task, plans)

      // 输出格式化结果
      const formattedResult = PlanOperationsSimulator.formatUpdatedTasksVisual(res)
      logger.log({ chat_id, planner: true }, formattedResult)
    } catch (error) {
      logger.error({ chat_id, planner: true }, 'BigPlanner 执行任务操作失败:', error)
    }
  }


  private static isProtectedSOP(desc: string) {
    // ====== 1) SOP 白名单：这些 description 的任务禁止【更新/删除/合并】 ======
    const PROTECTED_DESCRIPTIONS = [
      // 在这里填你的受保护任务文案
      'day1早上通知第一课（分为进群和未进群人群）【所有人】',
      'day1确认所有人有上课权限',
      'DAY1 晚上8点的通知',
      'day2 完课人群发送作业模版财富果园画面',
      'day2 8点上课通知',
      'day3 8点上课通知',
      'Day4调研加播课的主题',
      'day4课前十分钟提醒',
      'day5文字总结系统课精华',
      'day5 发送分手信，为客户总结这段时间旅程，对其的观察，拉进客户距离以及激发客户重新神奇系统的价值'
    ]

    return  PROTECTED_DESCRIPTIONS.some((keyword) => desc.includes(keyword))
  }

  /**
   * 执行计划操作：处理新增、更新、删除、合并任务
   * @param chat_id 聊天ID
   * @param plans 计划操作
   * @param currentTasks planner 中的任务
   * @param todayTasks 今日所有任务
   * @param think
   */
  private static async executePlanOperations(
    chat_id: string,
    plans: PlanOperations,
    currentTasks: ITask[],
    todayTasks: { id: string, description: string, time: string }[],
    think: string
  ) {
    // 今天 0 点
    const startOfToday = dayjs().startOf('day').toDate()
    // 今天晚上 0 点（也就是明天 0 点）
    const endOfToday = dayjs().endOf('day').toDate()

    const sopJobs = await Planner.filterSOPByDate(chat_id, startOfToday, endOfToday)


    // 1. 处理新增任务
    if (plans.toAdd && plans.toAdd.length > 0) {
      const tasksToAdd: ICreateTask[] = []
      const sendTimes: (string | undefined)[] = []

      let lastTs: number | null = null
      plans.toAdd.forEach((task) => {
        if (task && typeof task === 'object' && task.content && task.send_time) {
          let send_time: dayjs.Dayjs

          // 如果是完整日期时间（如 "2025-09-15 19:00"）
          if (/^\d{4}-\d{2}-\d{2}/.test(task.send_time)) {
            send_time = dayjs(task.send_time)
          } else {
            // 否则只拼接今天的日期 + 时间（如 "19:00"）
            send_time = dayjs(`${dayjs().format('YYYY-MM-DD')} ${task.send_time}`)
          }

          if (!send_time.isValid()) return

          const ts = send_time.valueOf()

          // 如果遇到下降（跨天），直接停止后续处理
          if (lastTs !== null && ts < lastTs) return

          // 时间过去了，不执行了
          if (send_time.toDate() < new Date()) {
            logger.log({ chat_id, planner: true }, `BigPlanner 添加任务: ${ task.content}，但时间已过，不执行`)
            return
          }

          tasksToAdd.push({
            description: task.content,
            sendTime: send_time.format(),
            type: task.type
          })
          sendTimes.push(send_time.format())

          lastTs = ts
        }
      })

      // 创建任务
      const createResult = await TaskManager.createTasks(
        chat_id,
        tasksToAdd,
        think, // 默认目标
        undefined // 不指定 round_id
      )

      // 如果有 send_time，更新任务的 send_time 字段
      if (createResult && createResult.tasks) {
        for (let i = 0; i < sendTimes.length && i < createResult.tasks.length; i++) {
          // 添加任务到任务队列
          await Planner.addDelayedTask(chat_id, [createResult.tasks[i].id])
        }
      }

      // logger.log({ chat_id, planner: true }, `BigPlanner 新增任务: ${tasksToAdd.length} 个`)
    }

    // 2. 处理更新任务
    if (plans.toUpdate && plans.toUpdate.length > 0) {
      for (const updateItem of plans.toUpdate) {
        const taskIndex = parseInt(updateItem.id, 10) - 1
        if (taskIndex >= 0 && taskIndex < todayTasks.length) {
          const todayTask = todayTasks[taskIndex]

          if (this.isProtectedSOP(todayTask.description)) {
            logger.log({ chat_id, planner: true }, `SOP 任务 ${todayTask.description} 受保护，不进行更新`)
            continue
          }

          // 非 SOP Task
          const matchedTask = currentTasks.find(
            (task) => task.description === todayTask.description
          )

          if (matchedTask) {
            await TaskManager.updateTask(matchedTask.id, {
              description: updateItem.content,
              type: updateItem.type
            })
            // logger.log({ chat_id, planner: true }, `BigPlanner 更新任务: ${matchedTask.id} -> ${updateItem.content}`)
          } else {
            // 时间过去了，不执行了
            if (new Date(todayTask.time) < new Date()) {
              logger.log({ chat_id, planner: true }, `BigPlanner 更新任务: ${todayTask.description} -> ${updateItem.content}，但时间已过，不执行`)
              continue
            }

            // SOP 转 Task Schedule 任务
            const { tasks } = await TaskManager.createTasks(chat_id, [
              {
                description: updateItem.content,
                sendTime: todayTask.time,
                type: updateItem.type
              }
            ], think)

            if (tasks.length > 0) {
              // 添加任务执行时间
              await Planner.addDelayedTask(chat_id, [tasks[0].id])
            }

            // 需要去 cancel 对应的 SOP
            await sopJobs.find((sop) => sop.name === todayTask.description)?.remove()
          }
        }
      }
    }

    // 3. 处理删除任务
    if (plans.toRemove && plans.toRemove.length > 0) {
      for (const removeId of plans.toRemove) {
        const taskIndex = parseInt(removeId, 10) - 1 // 转换为 0 基索引
        if (taskIndex >= 0 && taskIndex < todayTasks.length) {
          const todayTask = todayTasks[taskIndex]

          if (this.isProtectedSOP(todayTask.description)) {
            logger.log({ chat_id, planner: true }, `SOP 任务 ${todayTask.description} 受保护，不进行删除`)
            continue
          }

          // 在当前活跃任务中通过描述匹配找到对应任务
          const matchedTask = currentTasks.find((task) =>
            task.description === todayTask.description ||
            task.description.includes(todayTask.description) ||
            todayTask.description.includes(task.description)
          )

          if (matchedTask) {
            // 删除普通任务
            await TaskManager.updateStatus(matchedTask.id, TaskStatus.CANCELED)
            // logger.log({ chat_id, planner: true }, `BigPlanner 删除任务: ${matchedTask.id}`)
          } else {
            // 如果没有找到普通任务，可能是SOP任务，尝试删除对应的SOP
            const matchedSopJob = sopJobs.find((sop) => sop.name === todayTask.description)
            if (matchedSopJob) {
              await matchedSopJob.remove()
              // logger.log({ chat_id, planner: true }, `BigPlanner 删除SOP任务: ${todayTask.description}`)
            }
          }
        }
      }
    }

    // 4. 处理合并任务
    if (plans.toMerge && plans.toMerge.length > 0) {
      for (const mergeItem of plans.toMerge) {
        const targetIndex = parseInt(mergeItem.into, 10) - 1 // 转换为 0 基索引
        if (targetIndex >= 0 && targetIndex < todayTasks.length) {
          const targetTodayTask = todayTasks[targetIndex]

          // --- 白名单拦截：目标受保护 -> 整个合并跳过 ---
          if (this.isProtectedSOP(targetTodayTask.description)) {
            logger.log({ chat_id, planner: true }, `SOP 任务 ${targetTodayTask.description} 受保护，不进行合并`)
            continue
          }

          // 找到目标任务
          const targetTask = currentTasks.find((task) =>
            task.description === targetTodayTask.description ||
            task.description.includes(targetTodayTask.description) ||
            targetTodayTask.description.includes(task.description)
          )

          if (targetTask) {
            // 目标是普通任务，更新目标任务的描述为合并后的内容
            await TaskManager.updateTask(targetTask.id, {
              description: mergeItem.mergedContent,
              type: mergeItem.type,
              send_time: targetTask.send_time ?? undefined
            })
          } else {
            // 目标是SOP任务，需要删除原SOP并创建新的delayed task
            const targetSopJob = sopJobs.find((sop) => sop.name === targetTodayTask.description)
            if (targetSopJob) {
              // 删除原来的SOP
              await targetSopJob.remove()

              // 创建新的delayed task
              const { tasks } = await TaskManager.createTasks(chat_id, [
                {
                  description: mergeItem.mergedContent,
                  sendTime: targetTodayTask.time,
                  type: mergeItem.type
                }
              ], think)

              if (tasks.length > 0) {
                // 添加任务到消息队列
                await Planner.addDelayedTask(chat_id, [tasks[0].id])
              }
            }
          }

          // 删除源任务（包括普通任务和SOP任务）
          for (const fromId of mergeItem.from) {
            const fromIndex = parseInt(fromId, 10) - 1
            if (fromIndex >= 0 && fromIndex < todayTasks.length) {
              const fromTodayTask = todayTasks[fromIndex]

              // 尝试找到普通任务
              const fromTask = currentTasks.find((task) =>
                task.description === fromTodayTask.description ||
                task.description.includes(fromTodayTask.description) ||
                fromTodayTask.description.includes(task.description)
              )

              if (fromTask && (!targetTask || fromTask.id !== targetTask.id)) {
                // 删除普通任务
                await TaskManager.updateStatus(fromTask.id, TaskStatus.CANCELED)
              } else {
                // 如果没有找到普通任务，可能是SOP任务
                const fromSopJob = sopJobs.find((sop) => sop.name === fromTodayTask.description)
                if (fromSopJob) {
                  await fromSopJob.remove()
                }
              }
            }
          }

          // logger.log({ chat_id, planner: true }, `BigPlanner 合并任务: ${mergeItem.from.join(',')} -> ${mergeItem.into}`)
        }
      }
    }
  }

  public static async generatePlan(chat_id: string) {
    const prompt = await getPrompt('free-big-plan')

    const context = await this.buildContext(chat_id)
    logger.log({ chat_id, planner: true }, `Planner 输入: ${JSON.stringify(context, null, 2)}`)

    const result = await LLM.predict(prompt, { model: 'gpt-5', maxTokens: 4000, responseJSON: true, promptName: 'freeBigPlan', meta: { chat_id } }, context)

    // 解析 LLM 返回的结果
    let planResponse: PlanResponse
    try {
      planResponse = JSONHelper.parse(result) as PlanResponse
      if (!planResponse || !planResponse.plans) {
        throw new Error('Invalid plan response structure')
      }
    } catch (error) {
      logger.error({ chat_id, planner: true }, 'BigPlanner 解析 JSON 失败:', error)
      return
    }

    logger.log({ chat_id, planner: true }, `Planner 思考过程: ${planResponse.think}`)
    logger.log({ chat_id, planner: true }, `生成任务: ${JSON.stringify(planResponse.plans, null, 4)}`)

    return {
      planResponse,
      context
    }
  }
}
