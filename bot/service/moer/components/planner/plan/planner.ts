import dayjs from 'dayjs'
import logger from '../../../../../model/logger/logger'
import { ICreateTask, TaskManager } from '../task/task_manager'
import { Job, Queue } from 'bullmq'
import { getBotId, getUserId } from '../../../../../config/chat_id'
import { RedisDB } from '../../../../../model/redis/redis'
import { SchedulePlanItem, TaskScheduler } from '../task/task_scheduler'
import { listSOPByChatId } from '../../flow/schedule/task_starter'
import { SalesNodeHelper } from '../../flow/helper/salesNodeHelper'
import { ITask } from '../types'
import { RedisCacheDB } from '../../../../../model/redis/redis_cache'
import { PrismaMongoClient } from '../../../../../model/mongodb/prisma'
import { TaskWorker } from '../task/task_worker'
import { TaskStatus } from '@prisma/client'
import { ContextBuilder } from '../../agent/context'
import { UUID } from '../../../../../lib/uuid/uuid'
import { FreeTalk } from '../../agent/freetalk'
import { getState } from '../../flow/schedule/task/baseTask'
import { LLMNode } from '../../flow/nodes/llm'

export interface ISchedulePlannerTask {
  id: string

  send_time: string // YYYY-MM-DD HH:MM:SS 或 "now"
  description: string // 任务描述
  chat_id: string
}

export class Planner {
  /**
   * 将任务转为定时发送的 SOP
   * @param chat_id
   * @param round_id
   * @param tasks
   */
  public static async taskSchedule(chat_id: string, round_id: string, tasks:  ITask[]) {
    // context 拼装
    const currentTime = await ContextBuilder.temporalInformation(chat_id)

    const tasksToSchedule = tasks
      .filter((task) => task.send_time === null) // 已经规划过的任务，不再进行二次规划
      .map((task) => {
        return {
          task_id: task.id.slice(-4),
          task_description: task.description
        }
      })

    // 获取 visualized sop 和 planner 消息队列
    const existing_schedule = await this.filterTasksByDate(chat_id, new Date(), new Date(Date.now() + 3 * 24 * 60 * 60 * 1000))

    const customerBehavior = await ContextBuilder.getCustomerPortrait(chat_id)
    const dialogHistory = await SalesNodeHelper.getChatHistory(chat_id, 3, 10)

    const scheduledTask =  await new TaskScheduler(chat_id).scheduleTask({
      user_profile: `${ customerBehavior }\n\n${ dialogHistory }`,
      current_time: currentTime,
      tasks_to_schedule: tasksToSchedule,
      existing_schedule: existing_schedule
    })

    // 对 task Id 进行还原
    const idMap = tasks.reduce((map, task) => {
      map[task.id.slice(-4)] = task.id
      return map
    }, {} as Record<string, string>)


    // 假设 scheduledTask 是 [{ task_id: 'a1b2', ... }, ...]
    const scheduledTasks = scheduledTask
      .map((item) => {
        const fullTaskId = idMap[item.task_id]
        if (!fullTaskId) {
          logger.error('任务调度', `无效的任务ID: ${item.task_id}`)
          return null
        }
        return {
          ...item,
          task_id: fullTaskId // 还原完整 id
        }
      })
      .filter((task) => task !== null) as SchedulePlanItem[]

    // 更新发送时间 - 只处理有效的任务
    await Promise.allSettled(scheduledTasks.map(async (task) => {
      // scheduled_time 要被 new Date() 调用，要保证为有效的字符串格式
      await TaskManager.updateTask(task.task_id, { send_time: task.scheduled_time === 'now' ? new Date().toLocaleString() : task.scheduled_time })
    }))


    logger.log({ chat_id, round_id, planner: true }, '沉默后规划任务:', JSON.stringify(scheduledTasks, null, 4))
    return scheduledTasks
  }

  /**
   * 添加延迟任务到消息队列
   * @param chat_id
   * @param task_ids
   */
  public static async addDelayedTask(chat_id: string, task_ids: string[]) {
    const queue = new Queue<ISchedulePlannerTask>(this.getPlannerSOPQueueName(getBotId(chat_id)), {
      connection: RedisDB.getInstance()
    })

    const jobs: {name: string, data: any, opts: any}[] = []

    // 通过 task_id 查询出原始的 task
    for (const taskId of task_ids) {
      const task = await TaskManager.getTaskById(taskId)

      if (!task) continue
      if (task.status !== 'TODO') continue

      try {
        jobs.push({
          name: task.description,
          data: task,
          opts: { delay: new Date(task.send_time as string).getTime() - Date.now() - 2 * 60 * 1000 } // 留出 2分钟思考执行时间
        })
      } catch (e) {
        logger.error(e)
      }
    }

    await queue.addBulk(jobs)
  }

  public static getPlannerSOPQueueName(botId:string) {
    return `moer-planner-sop-${botId}`
  }

  private static async listPlannerTaskByChatId(chatId: string): Promise<Job[]> {
    const queue = new Queue<ISchedulePlannerTask>(this.getPlannerSOPQueueName(getBotId(chatId)), {
      connection: RedisDB.getInstance()
    })

    const allJobs = await queue.getDelayed()
    return allJobs.filter((item) => item.data.chat_id === chatId)
  }

  public static async filterSOPByDate(chatId: string, startDate: Date, endDate: Date) {
    // 一个小工具函数：获取当前时间窗内的延迟 Job
    const getWindowedJobs = async () => {
      const sops = await listSOPByChatId(chatId)
      return sops.filter((item) => {
        const jobTime = new Date(item.delay + item.timestamp)
        return jobTime >= startDate && jobTime <= endDate
      })
    }

    // 1) 先拿到窗口内的 Job
    let filteredSOPs = await getWindowedJobs()

    // 2) 按 SOP 原始 ID（即 BullMQ Job 的 name，在后续映射为 title 之前）去重
    let repeatedCount = 0
    const seen = new Set<string>()
    for (const sop of filteredSOPs) {
      const sopId = String(sop.name) // 这里的 name 仍是 “可视化SOP的ID”，尚未映射为标题
      if (seen.has(sopId)) {
        try {
          await sop.remove()
          repeatedCount++
        } catch (e) {
          logger.error('删除重复 SOP 失败', { sopId, error: e })
        }
      } else {
        seen.add(sopId)
      }
    }

    // 3) 如果有删除过重复项，重新获取一次窗口内的 Job，确保返回的是最新集合
    if (repeatedCount > 0) {
      filteredSOPs = await getWindowedJobs()
    }

    // redis 中取回对应的 title
    const sopValue = await new RedisCacheDB(`moer:${getBotId(chatId)}:visualized_sop`).get()
    // 构建 SOP Map
    const sopMap = new Map()

    for (const sop of sopValue) {
      sopMap.set(sop.id, sop.title)
    }

    for (const sop of filteredSOPs) {
      if (!sopMap.has(sop.name)) {
        logger.error('没有这个sop', sop.name)
        continue
      }
      sop.name = sopMap.get(sop.name)
    }

    return filteredSOPs
  }


  public static async filterTasksByDate(chatId: string, startDate: Date, endDate: Date) {
    const plannerTasks = await Planner.listPlannerTaskByChatId(chatId)

    const filteredPlannerSOPs = plannerTasks.filter((item) => {
      const jobTime = new Date(item.delay + item.timestamp)
      return jobTime >= startDate && jobTime <= endDate
    })

    const filteredSOPs = await Planner.filterSOPByDate(chatId, startDate, endDate)

    // 合并 以 时间排序
    const mergedSOPs = filteredSOPs.map((item) => {
      return {
        description: item.name,
        time: new Date(item.delay + item.timestamp)
      }
    }).concat(filteredPlannerSOPs.map((item) => {
      return {
        description: item.name,
        time: new Date(item.delay + item.timestamp)
      }
    })).sort((a, b) => a.time.getTime() - b.time.getTime())

    // 描述 + 时间
    return mergedSOPs.map((item) => {
      return {
        description: item.description,
        time: dayjs(item.time).format('YYYY-MM-DD HH:mm:ss')
      }
    })
  }


  static async executeImmediateTask(chat_id: string, scheduledTasks: SchedulePlanItem[]) {
    // 获取任务描述进行合并
    // 执行任务
    // 更新任务状态
    const immediateTasks = scheduledTasks.filter((item) => item.scheduled_time === 'now')

    const tasks = await PrismaMongoClient.getInstance().task.findMany({
      where: {
        chat_id,
        id: {
          in: immediateTasks.map((item) => item.task_id)
        }
      }
    })

    // 合并 Task 描述
    const taskDescription = tasks.filter((item) => item.status === 'TODO').map((item, index) => `${index + 1}. ${item.description}`).join('\n')

    // 更新任务状态
    await Promise.all(tasks.map((task) => TaskManager.updateStatus(task.id, TaskStatus.DONE)))


    if (taskDescription.trim() === '') {
      return
    }

    // 即时任务 直接调用 FreeTalk 执行
    await LLMNode.invoke({
      state: await getState(chat_id, getUserId(chat_id)),
      model: 'gpt-5-chat',
      useRAG: true,
      recallMemory: true,
      chatHistoryRounds: 6,
      promptName: 'free_talk',
      noStagePrompt: true,
      dynamicPrompt: taskDescription
    })

    // await TaskWorker.processTask(chat_id, taskDescription)
  }

  static async addPassiveTasks(chat_id: string, task: string[], round_id: string) {
    const toAddTasks = task.map((item) => ({ id: UUID.short().slice(0, 4), description: item }))
    const tasks =  await TaskManager.getFlexibleActiveTasks(chat_id)


    let finalAddedTasks: ICreateTask[] // 最终添加的任务
    if (toAddTasks.length + tasks.length > 5) {
      const remainingTasks = tasks.filter((item) => {
        return item.created_at && new Date(item.created_at).getDate() === new Date().getDate()
      })

      const toRemoveTasks = tasks.filter((item) => {
        return item.created_at && new Date(item.created_at).getDate() !== new Date().getDate()
      })

      await TaskManager.cancelTasks(toRemoveTasks.map((item) => item.id))

      const filteredTasks = await TaskManager.mergeTasks(chat_id, toAddTasks, remainingTasks, round_id)
      finalAddedTasks = filteredTasks.map((item) => ({ description: item.description, type: 'flexible' }))
    } else {
      finalAddedTasks = task.map((item) => ({ description: item, type: 'flexible' }))
    }

    logger.log({ chat_id, round_id, planner: true }, '添加被动任务:', JSON.stringify(finalAddedTasks, null, 2))

    await TaskManager.createTasks(chat_id, finalAddedTasks, '', round_id)
  }
}