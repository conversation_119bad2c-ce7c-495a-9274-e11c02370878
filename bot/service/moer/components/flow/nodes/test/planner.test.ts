import * as path from 'path'
import { FileHelper } from '../../../../../../lib/file'
import { DataService } from '../../../../getter/getData'
import { Config } from '../../../../../../config/config'
import { loadConfigByAccountName } from '../../../../../../../test/tools/load_config'
import { ChatHistoryService } from '../../../chat_history/chat_history'

interface ConversationPair {
  userMessage: string;
  userSendTime: string;
  assistantMessage: string;
  assistantSendTime: string;
}

describe('Test', function () {
  beforeAll(() => {

  })

  it('准备数据', async () => {
    // 读取数据文件
    const boluomeiData = await FileHelper.readFile(path.join(__dirname, 'salesDataSet/boluomei.txt'))
    const rongData = await FileHelper.readFile(path.join(__dirname, 'salesDataSet/rong.txt'))

    // 处理数据
    const boluomeiPairs = parseConversationData(boluomeiData)
    const rongPairs = parseConversationData(rongData)

    console.log('菠萝妹对话数据:', JSON.stringify(boluomeiPairs, null, 2))
    console.log('蓉对话数据:', JSON.stringify(rongPairs, null, 2))
  })

  it('准备客户数据', async () => {

  }, 30000)

  // it('Planner 测试', async () => {
  //   Config.setting.wechatConfig = await loadConfigByAccountName('syq')
  //
  //   // 跑一下 planner 的效果
  //   // 读取数据文件
  //
  //   // 准备数据
  //   const data = [
  //     // fs.readFileSync(path.join(__dirname, 'salesDataSet/boluomei.txt'), 'utf-8'),
  //     // fs.readFileSync(path.join(__dirname, 'salesDataSet/rong.txt'), 'utf-8'),
  //     fs.readFileSync(path.join(__dirname, 'salesDataSet/68AA沐恩413.txt'), 'utf-8'),
  //     fs.readFileSync(path.join(__dirname, 'salesDataSet/天上的小草.txt'), 'utf-8'),
  //     fs.readFileSync(path.join(__dirname, 'salesDataSet/杨红.txt'), 'utf-8'),
  //     fs.readFileSync(path.join(__dirname, 'salesDataSet/陈桂莲.txt'), 'utf-8'),
  //     fs.readFileSync(path.join(__dirname, 'salesDataSet/静旭.txt'), 'utf-8')
  //   ]

  // 每次进入，判断生成 plan -> plan 取出第一条注入到 回复
  // 每轮执行结束后，如果有 plan，直接认为是 沉默，将剩余消息取出，作为 SOP 发送

  //     const conversationPairs =  data.map((d) => parseConversationData(d))  // 处理数据
  //
  //     for (const conversationPair of conversationPairs) {
  //       const user_id = UUID.short()
  //       const chat_id = `test_${ getChatId(user_id)}`
  //
  //       await DataService.saveChat(chat_id, user_id)
  //
  //       for (const pair of conversationPair) {
  //         await WorkFlow.step(chat_id, user_id, pair.userMessage)
  //
  //         // 检查是否有 planner
  //         const currentPlan = await PlannerManager.getCurrentPlan(chat_id)
  //         if (currentPlan) {
  //           await PlannerManager.executeTask(chat_id) // 默认当前执行了
  //
  //           // 剩下当做 SOP 处理
  //           await ChatHistoryService.addUserMessage(chat_id, '【沉默】')
  //
  //           const state = await getState(chat_id, user_id, '【沉默】')
  //
  //           await LLMNode.invoke({
  //             state,
  //             model: 'gpt-5',
  //             useRAG: true,
  //             temperature: 1,
  //             recallMemory: true,
  //             chatHistoryRounds: 6,
  //             promptName: 'free_talk',
  //             noStagePrompt: true,
  //             dynamicPrompt: `现在客户正在沉默，你需要主动发送一条激活客户的消息：
  // 当前目标：${currentPlan.overall_goal}
  // 当前任务：${currentPlan.remaining_tasks.join('\n')}`
  //           })
  //
  //           await PlannerManager.completePlan(chat_id)
  //         }
  //       }

  // await DataService.saveChat(chat_id, user_id)
  //
  // console.log(chat_id)
  // }
  // }, 1E8s)

  it('存一下 chat', async () => {
    Config.setting.wechatConfig = await loadConfigByAccountName('syq')
    await DataService.saveChat('local_4AbKM8rKUu6MT5kggaffd9', '4AbKM8rKUu6MT5kggaffd9')
  }, 30000)


  it('获取两段对话记录，比较一下', async () => {
    // http://116.62.164.13:3001/user/chat/local_cTFGiCfrgxqkUf5xL119s7
    // http://116.62.164.13:3001/user/chat/local_4AbKM8rKUu6MT5kggaffd9
    const formatChatHistory1 = await ChatHistoryService.getFormatChatHistoryByChatId('local_cTFGiCfrgxqkUf5xL119s7')

    const formatChatHistory2 =  await ChatHistoryService.getFormatChatHistoryByChatId('local_4AbKM8rKUu6MT5kggaffd9')

    console.log(formatChatHistory1)
    console.log(formatChatHistory2)

  }, 30000)
})

function parseConversationData(data: string): ConversationPair[] {
  const lines = data.split('\n').filter((line) => line.trim())
  const conversations: ConversationPair[] = []

  // 预处理：合并多行消息
  const processedLines: string[] = []
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]
    // 检查是否是时间开头的行
    const timeMatch = line.match(/^(\d{4}年\d{1,2}月\d{1,2}日 \d{1,2}:\d{2}:\d{2})\s+([^：:]+)[：:](.*)$/)

    if (timeMatch) {
      processedLines.push(line)
    } else {
      // 续行，与前一行合并
      if (processedLines.length > 0) {
        processedLines[processedLines.length - 1] += `\n${line}`
      }
    }
  }

  let currentUserMessage = ''
  let currentUserTime = ''
  let currentAssistantMessage = ''
  let currentAssistantTime = ''
  let hasStartedProcessing = false // 标记是否开始处理（遇到第一个客户消息后）

  for (const line of processedLines) {
    const match = line.match(/^(\d{4}年\d{1,2}月\d{1,2}日 \d{1,2}:\d{2}:\d{2})\s+([^：:]+)[：:](.*)$/s)
    if (!match) continue

    const [, timestamp, speaker, message] = match
    const isAssistantMessage = speaker.includes('冥想助教')

    if (!isAssistantMessage) {
      // 客户消息
      if (hasStartedProcessing && currentUserMessage && currentAssistantMessage) {
        // 保存上一组对话
        conversations.push({
          userMessage: currentUserMessage,
          userSendTime: currentUserTime,
          assistantMessage: currentAssistantMessage,
          assistantSendTime: currentAssistantTime
        })
        // 重置助教消息
        currentAssistantMessage = ''
        currentAssistantTime = ''
      }

      // 开始新的客户消息
      hasStartedProcessing = true
      currentUserMessage = message
      currentUserTime = timestamp

    } else {
      // 助教老师消息
      if (hasStartedProcessing && currentUserMessage) {
        // 只有在开始处理且有客户消息的情况下才处理助教消息
        if (!currentAssistantMessage) {
          currentAssistantMessage = message
          currentAssistantTime = timestamp
        } else {
          // 合并多条助教消息
          currentAssistantMessage += `\n${  message}`
        }
      }
      // 忽略第一组客户发言之前的助教老师信息
    }
  }

  // 处理最后一组对话
  if (hasStartedProcessing && currentUserMessage && currentAssistantMessage) {
    conversations.push({
      userMessage: currentUserMessage,
      userSendTime: currentUserTime,
      assistantMessage: currentAssistantMessage,
      assistantSendTime: currentAssistantTime
    })
  }

  return conversations
}