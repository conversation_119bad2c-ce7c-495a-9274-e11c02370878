'use server'

import { chat } from '@prisma/client'
import axios from 'axios'
import { PrismaMongoClient } from '../../../bot/model/mongodb/prisma'
import { UserData } from '../type/user'
import { MoerNode } from '../../../bot/service/moer/components/flow/nodes/type'
import { IUserSlot } from '../../../bot/service/moer/storage/chat_state_store'
import { DataService } from '../../../bot/service/moer/getter/getData'

export async function queryChats(nameOrPhone:string, courseNo?:number):Promise<UserData[]> {
  const queryNameResult = await DataService.getChatByWechatName(nameOrPhone)
  if (!queryNameResult) {
    throw ('error')
  }
  const result = queryNameResult as unknown as any[]
  for (let i = 0; i < result.length; i++) {
    result[i].id = result[i]['_id']
  }

  return result.map((item) => transferChatIntoUserData(item))
}

export async function queryDefaultChats():Promise<UserData[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const queryNameResult = await mongoClient.chat.findMany({
    take:10,
    orderBy: {
      created_at: 'desc' // 按照 create_at 降序排列
    },
  })
  return queryNameResult.map((item) => transferChatIntoUserData(item))
}

export async function queryChatById(id:string):Promise<UserData | null> {
  const mongoClient = PrismaMongoClient.getInstance()
  const result = await mongoClient.chat.findFirst({ where:{
    id
  } })
  if (result) {
    return transferChatIntoUserData(result)
  } else {
    return null
  }
}

export async function queryChatsWithoutAi(courseNo?:number): Promise<UserData[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const result = await mongoClient.chat.findMany({ where:{
    is_human_involved:true,
    course_no:courseNo
  },
  take:courseNo ? undefined : 50,
  orderBy:{
    created_at:'desc'
  }
  })
  return result.map((item) => transferChatIntoUserData(item))
}

export async function queryChatsWithoutPhone(courseNo?:number):Promise<UserData[]> {
  const mongoClient = PrismaMongoClient.getInstance()
  const result = await mongoClient.chat.findRaw({
    filter: { 'chat_state.userSlots.phoneNumber': { $exists: false }, course_no: courseNo },
    options: {
      limit: 40,
      sort: {
        created_at: -1
      }
    } }) as unknown as any[]
  for (let i = 0; i < result.length; i++) {
    result[i].id = result[i]['_id']
  }
  return (result as chat[]).map((item) => transferChatIntoUserData(item))
}

export async function changeIsHumanInvolved(chatId:string, isHumanInvolved:boolean) {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data: { is_human_involved:isHumanInvolved } })
}

export async function changeIsStopGroupPush(chatId:string, isStopGroupPush:boolean) {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data: { is_stop_group_push:isStopGroupPush } })
}

export async function changeCourseNo(chatId:string, courseNo:number) {
  const mongoClient = PrismaMongoClient.getInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data:{ course_no:courseNo } })
}

export async function changePhone(chatId:string, phone:string) {
  throw ('not implement')
}

export async function changeNextStage(chatId:string, stage:MoerNode) {
  'use server'
  throw ('not implement')
  // const mongoClient = PrismaMongoClient.getInstance()
  // const chatInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
  // if (!chatInfo) {
  //   throw '没有找到这个人'
  // }
  // const mongoConfigInstance = PrismaMongoClient.getConfigInstance()
  // const botInfo = await mongoConfigInstance.config.findFirst({ select:{ address:true }, where:{ wechatId:chatInfo.wx_id } })
  // if (!botInfo) {
  //   throw '没有找到对应的机器人配置'
  // }
  // const address = botInfo.address
  // await axios(`${address}/test/event`, {
  //   method:'POST',
  //   data:{
  //     chatId: chatId,
  //     name: ITestEventType.ChangeNextStage,
  //     stage: stage
  //   }
  // }).then((res) => {
  //   if (res.data.code != 200) {
  //     throw res.data.msg
  //   }
  // })
}

export interface baseResponse {
  code:number
  msg:string
}

export async function getChatByCourseWeekRange(
  minCourseWeek: number,
  maxCourseWeek: number
) {
  const mongoClient = PrismaMongoClient.getInstance()
  const chatList = await mongoClient.chat.findMany({
    where: {
      course_no: {
        gte: minCourseWeek,
        lte: maxCourseWeek,
      },
    },
  })
  return chatList
}

function transferChatIntoUserData(chat:chat):UserData {
  return {
    id: chat.id,
    course_no: chat.course_no,
    contact: chat.contact,
    wx_id: chat.wx_id,
    is_human_involved: chat.is_human_involved,
    chat_state: {
      nextStage: chat.chat_state.nextStage
    },
    is_stop_group_push: chat.is_stop_group_push,
    phone: (chat.chat_state.userSlots as IUserSlot).phoneNumber ?? ''
  }
}