import { NextRequest, NextResponse } from 'next/server'
import { runCustomerPersonaFlow } from '@/lib/flow/customer-persona'
import { runPlannerFreeKickBatch } from '@/lib/flow/planner-freekick-batch'
import type { ExecutionTrace, FlowMode } from '@/types/flow'

interface ExecuteRequest {
  mode: FlowMode
  params: Record<string, unknown>
}

function parseChatIds(value: unknown): string[] {
  if (Array.isArray(value)) {
    return value
      .map((item) => String(item ?? '').trim())
      .filter((item) => item.length > 0)
  }

  const raw = String(value ?? '')
  return raw
    .split(/\r?\n/)
    .map((item) => item.trim())
    .filter((item) => item.length > 0)
}

export async function POST(request: NextRequest) {
  try {
    const payload = (await request.json()) as ExecuteRequest
    const mode = payload?.mode
    const params = payload?.params ?? {}

    if (mode === 'customerPersona') {
      const chatIds = parseChatIds(params.chatIds ?? params.chatId)

      if (chatIds.length === 0) {
        return NextResponse.json({ error: '请至少提供一个 chatId。' }, { status: 400 })
      }

      const aggregatedTrace: ExecutionTrace = []
      const personaRuns = []

      for (const chatId of chatIds) {
        const result = await runCustomerPersonaFlow({
          chatId,
          startDate: params.startDate ? String(params.startDate) : undefined,
          endDate: params.endDate ? String(params.endDate) : undefined,
          isDanmu: Boolean(params.isDanmu),
          loadExistingPortrait: Boolean(params.loadExistingPortrait),
        })

        personaRuns.push({
          chatId,
          portrait: result.portrait,
          rawOutput: result.rawOutput,
          chatHistory: result.chatHistory,
          roundId: result.roundId,
        })

        result.trace.forEach((step) => {
          aggregatedTrace.push({
            ...step,
            nodeId: `${step.nodeId}#${chatId}`,
          })
        })
      }

      return NextResponse.json({
        trace: aggregatedTrace,
        meta: {
          personaRuns,
        },
      })
    }

    if (mode === 'plannerFreeKick') {
      const chatIds = parseChatIds(params.chatIds ?? params.chatId)
      const selectedDays = params.selectedDays as string[]

      if (chatIds.length === 0) {
        return NextResponse.json({ error: '请至少提供一个 chatId。' }, { status: 400 })
      }

      // 检查是否是批量模式（选择了多个天数）
      // 批量模式：从 LangSmith 拉取数据并执行
      const aggregatedTrace: ExecutionTrace = []
      const batchResults = []

      for (const chatId of chatIds) {
        try {
          const results = await runPlannerFreeKickBatch({
            chatId,
            selectedDays,
            mainTask: params.mainTask as string,
            roundId: params.roundId as string
          })

          batchResults.push(...results)

          // 为每个批量结果创建 trace 条目
          results.forEach((result, index) => {
            aggregatedTrace.push({
              nodeId: `batch_${chatId}_${result.day}`,
              nodeType: 'plannerFreeKickBatch',
              inputData: result.plannerInput,
              outputData: {
                plannerOutput: result.plannerOutput,
                freeKickResults: result.freeKickResults,
                totalTasks: result.freeKickResults.length
              },
              error: result.error
            })
          })

        } catch (error) {
          aggregatedTrace.push({
            nodeId: `batch_${chatId}_error`,
            nodeType: 'plannerFreeKickBatch',
            inputData: { chatId, selectedDays },
            outputData: null,
            error: error instanceof Error ? error.message : String(error)
          })
        }
      }

      return NextResponse.json({
        trace: aggregatedTrace,
        meta: {
          batchResults,
          mode: 'batch'
        },
      })
    }
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : '未知错误'
    return NextResponse.json({ error: message }, { status: 500 })
  }
}
